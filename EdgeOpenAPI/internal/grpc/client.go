package grpc

import (
	"context"
	"encoding/base64"
	"time"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/configs"
	teaconst "github.com/TeaOSLab/EdgeOpenAPI/internal/const"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/encrypt"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/utils"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
)

// EdgeAPIClient wraps all EdgeAPI gRPC service clients
type EdgeAPIClient struct {
	conn                  *grpc.ClientConn
	APIAccessTokenService pb.APIAccessTokenServiceClient
	UserService           pb.UserServiceClient
	ServerService         pb.ServerServiceClient
	NodeService           pb.NodeServiceClient
	NodeClusterService    pb.NodeClusterServiceClient
	UserBillService       pb.UserBillServiceClient
	UserAccessKeyService  pb.UserAccessKeyServiceClient
	// Note: StatService doesn't exist in EdgeCommon, will use specific stat services as needed
}

// NewEdgeAPIClient creates a new EdgeAPI client with connection to the specified endpoint
func NewEdgeAPIClient(endpoint string) (*EdgeAPIClient, error) {
	// Configure gRPC connection with keepalive and retry settings
	conn, err := grpc.Dial(endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                10 * time.Second, // Send keepalive pings every 10 seconds
			Timeout:             time.Second,      // Wait 1 second for ping ack before considering the connection dead
			PermitWithoutStream: true,             // Send pings even without active streams
		}),
	)
	if err != nil {
		return nil, err
	}

	return &EdgeAPIClient{
		conn:                  conn,
		APIAccessTokenService: pb.NewAPIAccessTokenServiceClient(conn),
		UserService:           pb.NewUserServiceClient(conn),
		ServerService:         pb.NewServerServiceClient(conn),
		NodeService:           pb.NewNodeServiceClient(conn),
		NodeClusterService:    pb.NewNodeClusterServiceClient(conn),
		UserBillService:       pb.NewUserBillServiceClient(conn),
		UserAccessKeyService:  pb.NewUserAccessKeyServiceClient(conn),
	}, nil
}

// Close closes the gRPC connection
func (c *EdgeAPIClient) Close() error {
	return c.conn.Close()
}

// GetAPIAccessToken is a convenience method to get access token
func (c *EdgeAPIClient) GetAPIAccessToken(ctx context.Context, accessKeyId, accessKey string) (*pb.GetAPIAccessTokenResponse, error) {
	return c.APIAccessTokenService.GetAPIAccessToken(ctx, &pb.GetAPIAccessTokenRequest{
		Type:        "user",
		AccessKeyId: accessKeyId,
		AccessKey:   accessKey,
	})
}

// CreateAPIContext creates a context with API node credentials for gRPC calls
func (c *EdgeAPIClient) CreateAPIContext() context.Context {
	// Get configuration
	config := configs.GetConfig()
	if config == nil {
		// Fallback to context without proper authentication
		return context.Background()
	}

	// Create token data map
	var m = utils.Map{
		"timestamp": time.Now().Unix(),
		"type":      "api",
		"userId":    0,
	}

	// Use EdgeAPI's encryption method
	method, err := encrypt.NewMethodInstance(teaconst.EncryptMethod, config.APINode.Secret, config.APINode.NodeID)
	if err != nil {
		// Log error and return basic context
		return context.Background()
	}

	// Encrypt token data
	data, err := method.Encrypt(m.AsJSON())
	if err != nil {
		// Log error and return basic context
		return context.Background()
	}

	// Encode token
	token := base64.StdEncoding.EncodeToString(data)

	// Create context with metadata
	ctx := context.Background()
	ctx = metadata.AppendToOutgoingContext(ctx, "nodeId", config.APINode.NodeID, "token", token)
	return ctx
}
