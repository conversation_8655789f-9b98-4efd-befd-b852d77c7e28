package configs

import (
	"errors"
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config represents the complete EdgeOpenAPI configuration
type Config struct {
	Server    ServerConfig    `yaml:"server"`
	EdgeAPI   EdgeAPIConfig   `yaml:"edgeapi"`
	APINode   APINodeConfig   `yaml:"api_node"`
	Logging   LoggingConfig   `yaml:"logging"`
	CORS      CORSConfig      `yaml:"cors"`
	RateLimit RateLimitConfig `yaml:"rate_limit"`
	Security  SecurityConfig  `yaml:"security"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
	Mode string `yaml:"mode"` // gin mode: debug, release, test
}

// EdgeAPIConfig contains EdgeAPI connection settings
type EdgeAPIConfig struct {
	Endpoint  string          `yaml:"endpoint"`
	Timeout   time.Duration   `yaml:"timeout"`
	Keepalive KeepaliveConfig `yaml:"keepalive"`
}

// KeepaliveConfig contains gRPC keepalive settings
type KeepaliveConfig struct {
	Time                time.Duration `yaml:"time"`
	Timeout             time.Duration `yaml:"timeout"`
	PermitWithoutStream bool          `yaml:"permit_without_stream"`
}

// APINodeConfig contains API node credentials for EdgeAPI authentication
type APINodeConfig struct {
	NodeID string `yaml:"node_id"`
	Secret string `yaml:"secret"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level  string `yaml:"level"`  // debug, info, warn, error
	Format string `yaml:"format"` // json, text
	Output string `yaml:"output"` // stdout, stderr, or file path
}

// CORSConfig contains CORS settings
type CORSConfig struct {
	AllowedOrigins   []string `yaml:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers"`
	ExposeHeaders    []string `yaml:"expose_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
}

// RateLimitConfig contains rate limiting configuration
type RateLimitConfig struct {
	Enabled           bool `yaml:"enabled"`
	RequestsPerMinute int  `yaml:"requests_per_minute"`
	Burst             int  `yaml:"burst"`
}

// SecurityConfig contains security settings
type SecurityConfig struct {
	RequestTimeout time.Duration `yaml:"request_timeout"`
	MaxRequestSize string        `yaml:"max_request_size"`
}

var globalConfig *Config

// LoadConfig loads configuration from the specified file path
func LoadConfig(configPath string) (*Config, error) {
	// Default config path if not specified
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	// Read config file
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, errors.New("failed to read config file '" + configPath + "': " + err.Error())
	}

	// Parse YAML
	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, errors.New("failed to parse config file: " + err.Error())
	}

	// Validate configuration
	err = config.Validate()
	if err != nil {
		return nil, errors.New("config validation failed: " + err.Error())
	}

	// Set global config
	globalConfig = &config

	return &config, nil
}

// GetConfig returns the global configuration instance
func GetConfig() *Config {
	return globalConfig
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate server config
	if c.Server.Host == "" {
		c.Server.Host = "0.0.0.0"
	}
	if c.Server.Port == 0 {
		c.Server.Port = 8080
	}
	if c.Server.Mode == "" {
		c.Server.Mode = "release"
	}

	// Validate EdgeAPI config
	if c.EdgeAPI.Endpoint == "" {
		return errors.New("edgeapi.endpoint is required")
	}
	if c.EdgeAPI.Timeout == 0 {
		c.EdgeAPI.Timeout = 30 * time.Second
	}

	// Validate API node config
	if c.APINode.NodeID == "" {
		return errors.New("api_node.node_id is required")
	}
	if c.APINode.Secret == "" {
		return errors.New("api_node.secret is required")
	}

	// Set default logging config
	if c.Logging.Level == "" {
		c.Logging.Level = "info"
	}
	if c.Logging.Format == "" {
		c.Logging.Format = "json"
	}
	if c.Logging.Output == "" {
		c.Logging.Output = "stdout"
	}

	// Set default security config
	if c.Security.RequestTimeout == 0 {
		c.Security.RequestTimeout = 30 * time.Second
	}
	if c.Security.MaxRequestSize == "" {
		c.Security.MaxRequestSize = "10MB"
	}

	return nil
}

// GetServerAddress returns the server address in host:port format
func (c *Config) GetServerAddress() string {
	if c.Server.Port == 0 {
		return c.Server.Host + ":8080"
	}
	return c.Server.Host + ":" + fmt.Sprintf("%d", c.Server.Port)
}
